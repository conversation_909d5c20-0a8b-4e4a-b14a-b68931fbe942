globalThis.makoModuleHotUpdate('src/.umi/umi.ts?hmr', {
    modules: {
        "src/utils/request.ts": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            function _export(target, all) {
                for(var name in all)Object.defineProperty(target, name, {
                    enumerable: true,
                    get: all[name]
                });
            }
            __mako_require__.e(exports, {
                TokenManager: function() {
                    return TokenManager;
                },
                apiRequest: function() {
                    return apiRequest;
                },
                // 导出默认请求实例
                default: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _umirequest = __mako_require__("node_modules/umi-request/dist/index.esm.js");
            var _max = __mako_require__("src/.umi/exports.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            // 创建请求实例
            const request = (0, _umirequest.extend)({
                prefix: '/api',
                timeout: 10000,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            /**
 * Token 管理器（单令牌系统）
 *
 * 功能说明：
 * - 统一管理用户认证Token的存储和获取
 * - 使用localStorage进行持久化存储
 * - 支持Token的设置、获取、清除和检查
 *
 * 设计理念：
 * - 采用单令牌系统，简化认证流程
 * - Token同时用于用户认证和团队访问
 * - 提供静态方法，便于全局调用
 */ class TokenManager {
                static TOKEN_KEY = 'auth_token';
                /**
   * 获取当前Token
   */ static getToken() {
                    return localStorage.getItem(TokenManager.TOKEN_KEY);
                }
                /**
   * 设置Token
   */ static setToken(token) {
                    localStorage.setItem(TokenManager.TOKEN_KEY, token);
                }
                /**
   * 清除Token
   */ static clearToken() {
                    localStorage.removeItem(TokenManager.TOKEN_KEY);
                }
                /**
   * 检查是否有Token
   */ static hasToken() {
                    return !!TokenManager.getToken();
                }
            }
            /**
 * 请求拦截器
 *
 * 功能：
 * - 自动在请求头中添加Authorization Bearer Token
 * - 统一处理认证信息的注入
 * - 支持无Token的公开接口访问
 */ request.interceptors.request.use((url, options)=>{
                const token = TokenManager.getToken();
                if (token) {
                    // 添加Authorization头部
                    const headers = {
                        ...options.headers,
                        Authorization: `Bearer ${token}`
                    };
                    return {
                        url,
                        options: {
                            ...options,
                            headers
                        }
                    };
                }
                return {
                    url,
                    options
                };
            });
            /**
 * 响应拦截器
 *
 * 功能：
 * - 统一处理API响应格式
 * - 自动处理认证失效情况
 * - 统一的错误消息提示
 * - 自动跳转到登录页面
 * - 支持多种错误显示方式
 */ request.interceptors.response.use(async (response)=>{
                const data = await response.clone().json();
                // 检查业务状态码
                if (data.code !== 200) {
                    // 认证失败的处理
                    if (data.code === 401) {
                        // 检查当前路径，如果是Dashboard相关页面且刚刚进行了团队切换，
                        // 可能是Token更新的时序问题，不立即跳转
                        const currentPath = window.location.pathname;
                        const isDashboardRelated = currentPath.startsWith('/dashboard') || currentPath.startsWith('/team');
                        // 如果是Dashboard相关页面，延迟处理认证错误
                        if (isDashboardRelated) {
                            console.warn('Dashboard页面认证失败，可能是Token更新时序问题:', data.message);
                            return Promise.reject(new Error(data.message));
                        }
                        // 其他页面立即处理认证错误
                        TokenManager.clearToken();
                        _antd.message.error('登录已过期，请重新登录');
                        // 跳转到登录页，避免重复跳转
                        if (window.location.pathname !== '/user/login') _max.history.push('/user/login');
                        return Promise.reject(new Error(data.message));
                    }
                    // 处理权限错误
                    if (data.code === 403) {
                        // 显示后端返回的具体错误消息
                        _antd.message.error(data.message || '没有权限访问该资源');
                        return Promise.reject(new Error(data.message));
                    }
                    // 其他业务错误，显示错误消息
                    _antd.message.error(data.message || '请求失败');
                    return Promise.reject(new Error(data.message));
                }
                return response;
            }, (error)=>{
                // 网络错误或其他错误
                if (error.response) {
                    const { status } = error.response;
                    if (status === 401) {
                        // 检查当前路径，如果是Dashboard相关页面，可能是Token更新的时序问题
                        const currentPath = window.location.pathname;
                        const isDashboardRelated = currentPath.startsWith('/dashboard') || currentPath.startsWith('/team');
                        if (isDashboardRelated) {
                            console.warn('Dashboard页面认证失败，可能是Token更新时序问题');
                            // 不立即清除Token和跳转，让页面自己处理
                            return Promise.reject(error);
                        }
                        // 其他页面立即处理认证错误
                        TokenManager.clearToken();
                        _antd.message.error('登录已过期，请重新登录');
                        if (window.location.pathname !== '/user/login') _max.history.push('/user/login');
                    } else if (status === 403) {
                        var _error_response_data, _error_response;
                        // 检查是否是团队访问被拒绝的特殊错误
                        const errorMessage = (_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message;
                        if ((errorMessage === null || errorMessage === void 0 ? void 0 : errorMessage.includes('停用')) || (errorMessage === null || errorMessage === void 0 ? void 0 : errorMessage.includes('禁用')) || (errorMessage === null || errorMessage === void 0 ? void 0 : errorMessage.includes('不是该团队的成员'))) // 团队访问相关的错误，使用后端返回的具体消息
                        _antd.message.error(errorMessage);
                        else // 其他权限错误
                        _antd.message.error('没有权限访问该资源');
                    } else if (status === 404) _antd.message.error('请求的资源不存在');
                    else if (status >= 500) _antd.message.error('服务器错误，请稍后重试');
                    else _antd.message.error(`请求失败: ${status}`);
                } else if (error.request) // 网络连接错误
                _antd.message.error('网络错误，请检查网络连接');
                else // 其他错误
                _antd.message.error('请求失败，请重试');
                return Promise.reject(error);
            });
            const apiRequest = {
                get: (url, params)=>{
                    return request.get(url, {
                        params
                    });
                },
                post: (url, data)=>{
                    return request.post(url, {
                        data
                    });
                },
                put: (url, data)=>{
                    return request.put(url, {
                        data
                    });
                },
                delete: (url, params)=>{
                    return request.delete(url, {
                        params
                    });
                }
            };
            var _default = request;
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '15429485478756876544';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/team-management/index.tsx": [
            "src/pages/team-management/index.tsx"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=umi.1610373413373910254.hot-update.js.map